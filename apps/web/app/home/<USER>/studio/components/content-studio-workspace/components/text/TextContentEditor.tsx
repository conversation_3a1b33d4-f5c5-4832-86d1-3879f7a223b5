"use client";

import React, { useCallback, useEffect, useState, useMemo } from "react";

import { useParams } from "next/navigation";

import { Block } from "@blocknote/core";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { Loader2 } from "lucide-react";

import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { Button } from "@kit/ui/button";
import { Label } from "@kit/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kit/ui/tabs";
import { Textarea } from "@kit/ui/textarea";

import { SelectedDocument } from "~/components/document-selector";
import { useZero } from "~/hooks/use-zero";

import { useEditorContent } from "../../context/ContentStudioContext";
import { AdvancedOptions } from "./AdvancedOptions";

// Configuration for sb-server URL
const SB_SERVER_URL = process.env.NEXT_PUBLIC_SB_SERVER_URL || 'http://localhost:3000';

// import { useRouter } from 'next/navigation';
// interface GeneratedContent {
//   content: string;
//   cta_variations: string[];
//   headline: string;
//   rationale_for_creative_choices: string;
//   seo_keywords_used: string[];
//   trend_keywords_used: string[];
// }

export const TextContentEditor: React.FC = () => {
  // Get data from context
  const params = useParams();
  const contentId = params.id;
  const { editor } = useEditorContent();
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [companyContent] = useZeroQuery(
    zero.query.company_content.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const selectedCompanyContent = useMemo(() => {
    return companyContent.filter(
      (content: any) => content.id === contentId,
    )[0];
  }, [companyContent, contentId]);

  const [savedResearch] = useZeroQuery(
    zero.query.saved_research.where('account_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [personas] = useZeroQuery(
    zero.query.personas.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [icps] = useZeroQuery(
    zero.query.icps.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );
  // Add company_campaigns query
  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Add product_documents query
  const [productDocuments] = useZeroQuery(
    zero.query.product_documents.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Local state
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');

  // Advanced options state
  const [trendKeywords, setTrendKeywords] = useState<string[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocument[]>([]);
  const [selectedIcps, setSelectedIcps] = useState<any[]>([]);
  const [icpItems, setIcpItems] = useState<any[]>(icps || []);
  const [selectedPersonas, setSelectedPersonas] = useState<any[]>([]);
  const [personaItems, setPersonaItems] = useState<any[]>(personas || []);
  const [selectedResearch, setSelectedResearch] = useState<any[]>([]);
  const [researchItems, setResearchItems] = useState<any[]>(savedResearch || []);

  // Initialize task title and description from company content
  useEffect(() => {
    if (selectedCompanyContent) {
      setTaskTitle(selectedCompanyContent?.task_title || '');
      setTaskDescription(selectedCompanyContent?.task_description || '');
    }
  }, [selectedCompanyContent]);

  // Auto-update editor content when database changes (real-time streaming effect)
  // Try to apply minimal edits (append-only) to trigger BlockNote's cursor/typing animation.
  const prevBlocksRef = React.useRef<any[] | null>(null);

  function getBlockPlainText(block: any): string {
    if (!block?.content) return '';
    const walk = (nodes: any[]): string =>
      (nodes || []).map((n) => {
        if (n.type === 'text') return n.text || '';
        if (Array.isArray(n.content)) return walk(n.content);
        return '';
      }).join('');
    return walk(block.content);
  }

  useEffect(() => {
    const newBlocks = selectedCompanyContent?.content_editor_template;
    if (!editor || !Array.isArray(newBlocks)) return;

    const prevBlocks = prevBlocksRef.current;

    // First load or not generating → set full content once
    if (!prevBlocks || !selectedCompanyContent?.is_generating) {
      if (JSON.stringify(editor.document) !== JSON.stringify(newBlocks)) {
        editor.replaceBlocks(editor.document, newBlocks);
      }
      prevBlocksRef.current = newBlocks;
      return;
    }

    // Try incremental update when is_generating is true
    try {
      const prevLen = prevBlocks.length;
      const nextLen = newBlocks.length;

      // Compare all but the last block for semantic equality (text-only),
      // because server-generated IDs change on each update.
      const stableCount = Math.min(prevLen, nextLen) - 1;
      let stableEqual = true;
      for (let i = 0; i < stableCount; i++) {
        const prevText = getBlockPlainText(prevBlocks[i]);
        const nextText = getBlockPlainText(newBlocks[i]);
        if (prevText !== nextText) {
          stableEqual = false;
          break;
        }
      }

      if (!stableEqual) {
        // Fallback: replace if more than the last block changed
        editor.replaceBlocks(editor.document, newBlocks);
        prevBlocksRef.current = newBlocks;
        return;
      }

      // Handle same number of blocks → append text to last block
      if (prevLen === nextLen) {
        const prevLast = prevBlocks[prevLen - 1];
        const nextLast = newBlocks[nextLen - 1];
        const prevText = getBlockPlainText(prevLast);
        const nextText = getBlockPlainText(nextLast);

        if (nextText.startsWith(prevText)) {
          // Minimal update: update only the last editor block's content
          const lastEditorBlock = editor.document[editor.document.length - 1];
          if (lastEditorBlock?.id) {
            editor.updateBlock(lastEditorBlock.id, {
              type: nextLast.type,
              props: nextLast.props,
              content: nextLast.content,
            } as any);
            // Move cursor to end of last block for visual feedback
            editor.setTextCursorPosition(lastEditorBlock.id, 'end');
          } else {
            // Fallback if no block found
            editor.replaceBlocks(editor.document, newBlocks);
          }
          prevBlocksRef.current = newBlocks;
          return;
        }
      }

      // Handle one extra block appended → insert the new block, then update its content
      if (nextLen === prevLen + 1) {
        const prevLastStable = prevBlocks[prevLen - 1];
        const nextLastStable = newBlocks[nextLen - 2];
        if (JSON.stringify(prevLastStable) === JSON.stringify(nextLastStable)) {
          const appendedBlock = newBlocks[nextLen - 1];
          // Insert the new block at the end, using the last editor block id as reference
          const lastEditorBlock = editor.document[editor.document.length - 1];
          if (lastEditorBlock?.id) {
            editor.insertBlocks([appendedBlock], lastEditorBlock.id, 'after');
            // Update the just-inserted block's content (now the last block)
            const newBlockInEditor = editor.document[editor.document.length - 1];
            editor.updateBlock(newBlockInEditor.id, {
              type: appendedBlock.type,
              props: appendedBlock.props,
              content: appendedBlock.content,
            } as any);
            // Move cursor to end of new block for visual feedback
            editor.setTextCursorPosition(newBlockInEditor.id, 'end');
          } else {
            // Fallback if we cannot identify reference
            editor.replaceBlocks(editor.document, newBlocks);
          }
          prevBlocksRef.current = newBlocks;
          return;
        }
      }

      // Fallback
      editor.replaceBlocks(editor.document, newBlocks);
      prevBlocksRef.current = newBlocks;
    } catch (e) {
      // Safe fallback on any unexpected error
      editor.replaceBlocks(editor.document, newBlocks);
      prevBlocksRef.current = newBlocks;
    }
  }, [selectedCompanyContent?.content_editor_template, selectedCompanyContent?.is_generating, editor]);

  // Pre-populate advanced options from campaign data
  useEffect(() => {
    if (selectedCompanyContent?.campaign_id && companyCampaigns?.length > 0) {
      const associatedCampaign = companyCampaigns.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id,
      );
      if (associatedCampaign) {
        // Pre-populate target_icps - convert IDs to full objects
        if (
          associatedCampaign.target_icps &&
          Array.isArray(associatedCampaign.target_icps) &&
          icps?.length > 0
        ) {
          const icpObjects = associatedCampaign.target_icps
            .map((id: string) => icps.find((icp: any) => icp.id === id))
            .filter(Boolean)
            .map((icp: any) => ({
              id: icp.id,
              name: icp.name || 'ICP',
              data: icp.data
            }));
          setSelectedIcps(icpObjects);
        }

        // Pre-populate target_personas - convert IDs to full objects
        if (
          associatedCampaign.target_personas &&
          Array.isArray(associatedCampaign.target_personas) &&
          personas?.length > 0
        ) {
          const personaObjects = associatedCampaign.target_personas
            .map((id: string) => personas.find((persona: any) => persona.id === id))
            .filter(Boolean)
            .map((persona: any) => ({
              id: persona.id,
              name: persona.name || 'Persona',
              data: persona.data
            }));
          setSelectedPersonas(personaObjects);
        }

        // Pre-populate external_research - convert IDs to full objects
        if (
          associatedCampaign.external_research &&
          Array.isArray(associatedCampaign.external_research) &&
          savedResearch?.length > 0
        ) {
          const researchObjects = associatedCampaign.external_research
            .map((id: string) => savedResearch.find((research: any) => research.id === id))
            .filter(Boolean)
            .map((research: any) => ({
              id: research.id,
              title: research.title || research.topic || 'Research Item',
              research_type: research.research_type,
              description: research.description,
            }));
          setSelectedResearch(researchObjects);
        }

        // Pre-populate documents - convert product IDs to SelectedDocument objects
        if (
          associatedCampaign.products &&
          Array.isArray(associatedCampaign.products) &&
          productDocuments?.length > 0
        ) {
          const selectedDocs: SelectedDocument[] = associatedCampaign.products
            .map((docId: string) => {
              const doc = productDocuments.find((pd: any) => pd.id === docId);
              return doc
                ? {
                    id: doc.id,
                    documentTitle: doc.title,
                    content: doc.content || '',
                  }
                : null;
            })
            .filter(Boolean) as SelectedDocument[];

          setSelectedDocuments(selectedDocs);
        }
      }
    }
  }, [selectedCompanyContent, companyCampaigns, productDocuments, icps, personas, savedResearch]);

  // Utility function to convert text to BlockNote blocks
  const convertTextToBlocks = useCallback(async (text: string): Promise<Block[]> => {
    if (!editor) return [];
    
    try {
      // Use BlockNote's markdown parser
      const blocks = await editor.tryParseMarkdownToBlocks(text);
      return blocks;
    } catch (error) {
      console.error('Error parsing markdown to blocks:', error);
      
      // Fallback: split by paragraphs and create simple paragraph blocks
      const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');
      
      return paragraphs.map(paragraph => ({
        id: crypto.randomUUID(),
        type: 'paragraph',
        props: {},
        content: [{
          type: 'text',
          text: paragraph.trim(),
          styles: {},
        }],
        children: [],
      }));
    }
  }, [editor]);

  // Create a simplified function to generate content
  const generateContent = useCallback(async () => {
    setIsGenerating(true);
    setHasError(false);

    if (!selectedCompanyContent?.id || !editor) {
      setIsGenerating(false);
      return;
    }

    try {
      // Prepare content generation parameters for prompt compilation
      const contentParams = {
        taskDescription,
        selectedCompanyContent,
        selectedPersonas,
        personas,
        selectedIcps,
        icps,
        selectedResearch,
        researchItems,
        selectedDocuments,
        seoKeywords,
        trendKeywords,
        companyBrand,
        enableStreaming: false, // Disable server-side streaming
      };

      // Get compiled prompt from server
      const response = await fetch('/api/ai/generate-studio-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentParams),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get compiled prompt');
      }

      const result = await response.json();
      console.log('Compiled prompt result:', result);

      if (!result.compiled_prompt) {
        throw new Error('No compiled prompt received from server');
      }

      // Mark content as generating in database
      // @ts-expect-error - Zero mutator types are not properly inferred
      await zero.mutate.company_content.update({
        id: selectedCompanyContent.id,
        values: {
          is_generating: true,
        },
      });

      // Create the prompt block content
      const promptText = result.compiled_prompt.messages
        .map((msg: any) => msg.content || '')
        .join('\n\n');

      // Replace the entire document with the new prompt block
      editor.replaceBlocks(editor.document, [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: promptText,
              styles: {},
            },
          ],
        },
      ]);

      // Automatically select the prompt text so user can immediately use AI toolbar
      const firstBlock = editor.document[0];
      if (firstBlock) {
        editor.setTextCursorPosition(firstBlock, 'start');
        editor.setTextCursorPosition(firstBlock, 'end');
      }

      // Mark content as no longer generating since we've prepared the prompt
      // @ts-expect-error - Zero mutator types are not properly inferred
      await zero.mutate.company_content.update({
        id: selectedCompanyContent.id,
        values: {
          is_generating: false,
        },
      });

      setIsGenerating(false);
    } catch (error) {
      console.error('Error generating content:', error);
      setHasError(true);

      // Mark content as no longer generating on error
      if (selectedCompanyContent?.id) {
        try {
          // @ts-expect-error - Zero mutator types are not properly inferred
          await zero.mutate.company_content.update({
            id: selectedCompanyContent.id,
            values: {
              is_generating: false,
              error_generating: true,
            },
          });
        } catch (updateError) {
          console.error('Error updating generation status:', updateError);
        }
      }
    } finally {
      setIsGenerating(false);
    }
  }, [
    selectedCompanyContent,
    editor,
    taskDescription,
    selectedPersonas,
    selectedIcps,
    selectedResearch,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
    zero,
  ]);

  // Memoize the description change handler to prevent unnecessary re-renders
  const onDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setTaskDescription(newValue);

    if (selectedCompanyContent?.id) {
      // @ts-expect-error - Zero mutator types are not properly inferred
      zero.mutate.company_content.update({
        id: selectedCompanyContent.id,
        values: {
          task_description: newValue,
        },
      });
    }
  }, [selectedCompanyContent?.id, zero]);


  return (
    <div className="space-y-4 p-4">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-muted-foreground text-sm">
              Enter the topic or basis used to generate the content.
            </Label>

            <Textarea
              value={taskDescription}
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-muted-foreground text-sm">
              Enter the topic or basis used to generate the content.
            </Label>
            <Textarea
              value={taskDescription}
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
          <AdvancedOptions
            selectedDocuments={selectedDocuments}
            onDocumentsChange={setSelectedDocuments}
            selectedIcps={selectedIcps}
            onIcpsChange={setSelectedIcps}
            icps={icpItems}
            onIcpsListChange={setIcpItems}
            selectedPersonas={selectedPersonas}
            onPersonasChange={setSelectedPersonas}
            personas={personaItems}
            onPersonasListChange={setPersonaItems}
            trendKeywords={trendKeywords}
            onTrendKeywordsChange={setTrendKeywords}
            seoKeywords={seoKeywords}
            onSeoKeywordsChange={setSeoKeywords}
            selectedResearch={selectedResearch}
            onResearchChange={setSelectedResearch}
            researchItems={researchItems}
            onResearchItemsChange={setResearchItems}
          />
        </TabsContent>
      </Tabs>

      <Button
        onClick={generateContent}
        disabled={isGenerating || selectedCompanyContent?.is_generating || !taskTitle.trim() || !taskDescription.trim()}
      >
        {(isGenerating || selectedCompanyContent?.is_generating) ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {selectedCompanyContent?.is_generating ? 'Streaming content...' : 'Generating...'}
          </>
        ) : (
          'Generate'
        )}
      </Button>

      {/* Show streaming status */}
      {selectedCompanyContent?.is_generating && (
        <div className="text-sm text-blue-600 flex items-center">
          <Loader2 className="mr-2 h-3 w-3 animate-spin" />
          Content is being generated in real-time...
        </div>
      )}

      {hasError && (
        <div className="mt-2">
          <button
            onClick={generateContent}
            className="cursor-pointer text-sm text-red-600 underline hover:text-red-800"
          >
            Error Generating, Click to try again
          </button>
        </div>
      )}

      {/* {generatedContent && (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="keywords">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Keywords</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">SEO Keywords</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {generatedContent.seo_keywords_used.map((keyword, index) => (
                          <Badge key={index} variant="secondary">{keyword}</Badge>
                        ))}
                      </div>
                    </div>
                    {generatedContent.trend_keywords_used.length > 0 && (
                      <div>
                        <Separator className="my-4" />
                        <Label className="text-sm">Trend Keywords</Label>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {generatedContent.trend_keywords_used.map((keyword, index) => (
                            <Badge key={index} variant="outline">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="rationale">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Creative Rationale</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <p>{generatedContent.rationale_for_creative_choices}</p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

          </div>
        </ScrollArea>
      )} */}
    </div>
  );
};
