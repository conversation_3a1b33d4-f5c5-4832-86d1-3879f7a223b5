import { NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { z } from 'zod';

const StudioContentSchema = z.object({
  taskDescription: z.string(),
  selectedCompanyContent: z.object({
    channel: z.string().optional(),
    content_type: z.string().optional(),
  }),
  selectedPersonas: z.array(z.object({
    id: z.string(),
    name: z.string(),
    data: z.any().optional(),
  })).optional().default([]),
  personas: z.array(z.any()).optional().default([]),
  selectedIcps: z.array(z.object({
    id: z.string(),
    name: z.string(),
    data: z.any().optional(),
  })).optional().default([]),
  icps: z.array(z.any()).optional().default([]),
  selectedResearch: z.array(z.object({
    id: z.string(),
    title: z.string().optional(),
    topic: z.string().optional(),
    research_type: z.string().optional(),
    description: z.string().optional(),
    data: z.any().optional(),
  })).optional().default([]),
  researchItems: z.array(z.any()).optional().default([]),
  selectedDocuments: z.array(z.any()).optional().default([]),
  seoKeywords: z.array(z.string()).optional().default([]),
  trendKeywords: z.array(z.string()).optional().default([]),
  companyBrand: z.any().optional(),
});

export const POST = enhanceRouteHandler(
  async ({ body, user }) => {
    try {
      console.log('Generate studio content request from user:', user.id);
      console.log('Request body:', body);

      // Forward request to sb-server
      const pushServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
      const response = await fetch(`${pushServerUrl}/generate-studio-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error from sb-server:', errorText);
        return NextResponse.json(
          { error: 'Failed to generate content from server', details: errorText },
          { status: response.status }
        );
      }

      const result = await response.json();
      console.log('Generated content result:', result);

      return NextResponse.json(result, { status: 200 });
    } catch (error) {
      console.error('Error in generate-studio-content API route:', error);
      return NextResponse.json(
        {
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  },
  {
    auth: true,
    schema: StudioContentSchema,
  }
);
