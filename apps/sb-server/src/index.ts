import express, { Request, Response } from "express";
import dotenv from "dotenv";
import {
  PushProcessor,
  ZQLDatabase,
  PostgresJSConnection,
} from '@rocicorp/zero/pg';
import postgres from 'postgres';
import {schema} from './lib/zero-schema/schema.js';
import {createServerMutators} from './lib/server-mutators.js';
import { crawlWebsite, getCrawlStatus } from './lib/services/crawl-website.js';
import { generateContentSchedule } from './lib/services/generate-schedule.js';
import { generatePersona } from './lib/services/generate-persona.js';
import { scrapeWebsite } from './lib/services/scrape-website.js';
import { scrapeWebsiteByElement } from './lib/services/scrape-website-by-element.js';
import { generateVisualDescription } from './lib/services/generate-visual-description.js';
import { extractFromWebsite, getExtractStatus } from './lib/services/extract-from-website.js';
import { scrapeReddit } from './lib/services/scrape-reddit.js';
import { scrapeTikTok } from './lib/services/scrape-tiktok.js';
import { scrapeTwitter } from './lib/services/scrape-x.js';
import { generateCampaignName } from './lib/services/generate-campaign-name.js';
import { generateStudioContent } from './lib/services/generate-studio-content.js';
console.log('schema', schema);
dotenv.config();

const app = express();
const PORT = process.env.PORT || 8080;
const BASE_URL = process.env.BASE_URL || `http://localhost:${PORT}`;

app.use(express.json({ limit: "5mb" }));
app.use(express.urlencoded({ extended: true, limit: "5mb" }));

const processor = new PushProcessor(
  new ZQLDatabase(
    new PostgresJSConnection(postgres(process.env.ZERO_UPSTREAM_DB! as string)),
    schema
  )
);

app.get("/", (req: Request, res: Response) => {
  res.json({ message: "Hello World! TypeScript server is running." });
});

app.get("/env-test", (req: Request, res: Response) => {
  res.json({
    message: process.env.TEST_MESSAGE,
    environment: process.env.NODE_ENV,
    dbHost: process.env.DB_HOST,
    dbPort: process.env.DB_PORT,
  });
});

app.post("/push", async (req: Request, res: Response) => {
  console.log("push", req.body);
  const asyncTasks: Array<() => Promise<void>> = [];

  // Create a Web API compatible Request object
  const webRequest = new globalThis.Request(`${BASE_URL}${req.url}`, {
    method: "POST",
    headers: req.headers as HeadersInit,
    body: JSON.stringify(req.body),
  });

  // Use server mutators instead of client mutators
  const result = await processor.process(
    createServerMutators({ sub: "" }, asyncTasks),
    webRequest
  );

  // ✅ Execute async tasks AFTER the database transaction commits
  await Promise.all(asyncTasks.map((task) => task()));

  console.log("result", result);
  res.json(result);
});

app.get("/crawl", async (req: Request, res: Response) => {
  const result = await crawlWebsite(
    req.query.url as string,
    parseInt(req.query.limit as string),
    ["markdown"]
  );
  res.json(result);
});

app.post("/extract-from-website", async (req: Request, res: Response) => {
  const { urls, prompt, schema, enableWebSearch } = req.body;
  console.log(
    "urls",
    urls,
    "prompt",
    prompt,
    "schema",
    schema,
    "enableWebSearch",
    enableWebSearch
  );
  const result = await extractFromWebsite(
    urls,
    prompt,
    schema,
    enableWebSearch
  );
  res.json(result);
});

app.get("/extract-status", async (req: Request, res: Response) => {
  const extractId = req.query.extractId as string;
  console.log("extractId", extractId);
  const result = await getExtractStatus(extractId);
  res.json(result);
});

app.get("/crawl-status", async (req: Request, res: Response) => {
  const result = await getCrawlStatus(req.query.crawlId as string);
  res.json(result);
});

app.post("/generate-schedule", async (req: Request, res: Response) => {
  const {
    productDocumentation,
    campaignGoal,
    endDate,
    startDate,
    externalResearch,
  } = req.body;
  const result = await generateContentSchedule({
    productDocumentation,
    campaignGoal,
    startDate,
    endDate,
    externalResearch,
  });
  res.json(result);
});

app.post("/generate-persona", async (req: Request, res: Response) => {
  const { icp_data } = req.body;
  const result = await generatePersona(icp_data);
  res.json(result);
});

app.get("/scrape", async (req: Request, res: Response) => {
  try {
    const url = req.query.url as string;

    if (!url) {
      res.status(400).json({ error: "URL parameter is required" });
      return;
    }

    const result = await scrapeWebsite(url);
    res.json(result);
  } catch (error) {
    console.error("Error in scrape endpoint:", error);
    res.status(500).json({
      error: "Failed to scrape website",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

app.post("/scrape-by-element", async (req: Request, res: Response) => {
  const { url, element_prompts, json_output, instructions } = req.body;
  console.log(
    "url",
    url,
    "element_prompts",
    element_prompts,
    "json_output",
    json_output,
    "instructions",
    instructions
  );
  const result = await scrapeWebsiteByElement(
    url,
    element_prompts,
    json_output,
    instructions
  );
  res.json(result);
});

app.post('/generate-visual-description', async (req: Request, res: Response) => {
  const { brand_brief, content } = req.body;
  console.log('brand_brief', brand_brief, 'content', content, 'req.body', req.body);
  const result = await generateVisualDescription(brand_brief, content);
  res.json( {result} );
});

app.post("/generate-campaign-name", async (req: Request, res: Response) => {
  try {
    const { campaignInformation } = req.body;
    console.log("campaignInformation", campaignInformation);

    if (!campaignInformation) {
      res
        .status(400)
        .json({ error: "campaignInformation parameter is required" });
      return;
    }

    const result = await generateCampaignName(campaignInformation);
    res.json({
      success: true,
      campaignName: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in generate-campaign-name endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate campaign name",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/generate-studio-content", async (req: Request, res: Response) => {
  try {
    const contentParams = req.body;
    console.log("Studio content generation request:", contentParams);

    const result = await generateStudioContent(contentParams);
    console.log("AFTER GENERATION:", result);
    res.json({
      success: true,
      content: result.content,
      content_editor_template: result.content_editor_template,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in generate-studio-content endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate studio content",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Social Media Scraping Endpoints
app.post("/scrape/reddit", async (req: Request, res: Response) => {
  try {
    const params = req.body;
    console.log("Reddit scrape request:", params);

    const result = await scrapeReddit(params);
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in Reddit scrape endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to scrape Reddit",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/scrape/tiktok", async (req: Request, res: Response) => {
  try {
    const params = req.body;
    console.log("TikTok scrape request:", params);

    const result = await scrapeTikTok(params);
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in TikTok scrape endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to scrape TikTok",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/scrape/twitter", async (req: Request, res: Response) => {
  try {
    const params = req.body;
    console.log("Twitter scrape request:", params);

    const result = await scrapeTwitter(params);
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in Twitter scrape endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to scrape Twitter",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
