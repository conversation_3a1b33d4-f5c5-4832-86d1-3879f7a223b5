import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';
import {
  ContentGenerationParams,
  GeneratedContent
} from '../types/content-generation.js';
import {
  createPromptVariables,
  normalizeCompiledPrompt,
  validateCompiledPrompt,
  validateContentGenerationParams,
  ContentGenerationError
} from '../utils/content-generation.js';
import { ServerBlockNoteEditor } from '@blocknote/server-util';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Legacy interface for backward compatibility
interface TaskContentParams extends ContentGenerationParams {}

/**
 * Converts text content to BlockNote blocks format using official BlockNote server utilities
 */
async function convertTextToBlocks(text: string): Promise<any[]> {
  if (!text || text.trim() === '') {
    return [];
  }

  try {
    // Create a server-side BlockNote editor instance
    const editor = ServerBlockNoteEditor.create();

    // Use BlockNote's official markdown parser to convert text to blocks
    const blocks = await editor.tryParseMarkdownToBlocks(text);

    return blocks;
  } catch (error) {
    console.error('Error converting text to blocks with BlockNote:', error);

    // Fallback to simple paragraph blocks if BlockNote parsing fails
    const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');

    return paragraphs.map(paragraph => ({
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: paragraph.trim(),
          styles: {},
        },
      ],
    }));
  }
}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: ContentGenerationParams): Promise<string> {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });
  const promptVariables = createPromptVariables(params);
  const compiled = prompt.compile(promptVariables);
  const compiledText = normalizeCompiledPrompt(compiled);
  validateCompiledPrompt(compiledText);
  return compiledText;
}

/**
 * Generates content for a single task using Langfuse prompt and LLM
 */
export async function generateTaskContent(params: TaskContentParams): Promise<GeneratedContent> {
  try {
    // Validate input parameters
    validateContentGenerationParams(params);

    // Compile the prompt with parameters
    const promptObject = await compilePrompt(params);

    // Call LLM with compiled prompt
    const response = await callLLM(
      promptObject,
      {
        temperature: 0.7,
      }, // Provide temperature config
      "google/gemini-2.5-pro-preview",
      { parse: false }
    );

    // Convert text content to BlockNote blocks using official server utilities
    const contentBlocks = await convertTextToBlocks(response);

    // Return standardized response format
    return {
      content: response, // Keep for backward compatibility
      content_editor_template: contentBlocks,
      visual_description: null,
      seo_keywords: [],
      trend_keywords: []
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (error instanceof ContentGenerationError) {
      console.warn(`   ❌ Content generation validation failed: ${errorMessage}`);
      throw error;
    }

    if (errorMessage.includes('generate_content_body_v2')) {
      console.warn(`   ❌ Langfuse prompt failed: ${errorMessage}`);
      throw new ContentGenerationError(`Prompt compilation failed: ${errorMessage}`);
    }

    console.error('Error generating task content:', error);
    throw new ContentGenerationError(
      'Failed to generate task content. Please check your API key and try again.',
      undefined,
      undefined,
      error instanceof Error ? error : new Error(errorMessage)
    );
  }
}
