import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';
import { ServerBlockNoteEditor } from '@blocknote/server-util';
import OpenAI from 'openai';
import { observeOpenAI } from 'langfuse';
import postgres from 'postgres';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Database connection for real-time updates
const sql = postgres(process.env.ZERO_UPSTREAM_DB as string);

export interface StudioContentParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: any[];
  personas: any[];
  selectedIcps: any[];
  icps: any[];
  selectedResearch: any[];
  researchItems: any[];
  selectedDocuments: any[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
  enableStreaming?: boolean;
}

export interface StudioContentResult {
  content: string;
  content_editor_template: any[];
}

/**
 * Converts text content to BlockNote blocks format using official BlockNote server utilities
 */
async function convertTextToBlocks(text: string): Promise<any[]> {
  if (!text || text.trim() === '') {
    return [];
  }

  try {
    // Create a server-side BlockNote editor instance
    const editor = ServerBlockNoteEditor.create();

    // Use BlockNote's official markdown parser to convert text to blocks
    const blocks = await editor.tryParseMarkdownToBlocks(text);

    return blocks;
  } catch (error) {
    console.error('Error converting text to blocks with BlockNote:', error);

    // Fallback to simple paragraph blocks if BlockNote parsing fails
    const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');

    return paragraphs.map(paragraph => ({
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: paragraph.trim(),
          styles: {},
        },
      ],
    }));
  }
}

/**
 * Generate context blocks for audience information
 */
function generateAudienceContext(
  selectedPersonas: any[] = [],
  personas: any[] = [],
  selectedIcps: any[] = [],
  icps: any[] = []
): string {
  const personasSection =
    selectedPersonas && selectedPersonas.length > 0
      ? `
    <Personas>
        ${selectedPersonas
          .map((persona) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof persona === 'string') {
              // Backward compatibility: find persona by ID
              const personaObj = personas.find((p) => p.id === persona);
              if (personaObj) {
                const personaData = personaObj.data && typeof personaObj.data === 'object' ? personaObj.data : {};
                return `<Persona name="${personaObj.name}">\n<Description>${JSON.stringify((personaData as any).data) || 'Target persona'}</Description>\n</Persona>`;
              }
              return '';
            } else {
              // New format: use the persona object directly
              const personaData = persona.data && typeof persona.data === 'object' ? persona.data : {};
              return `<Persona name="${persona.name}">\n<Description>${JSON.stringify((personaData as any).data) || 'Target persona'}</Description>\n</Persona>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n')}
    </Personas>`
      : '';

  const icpsSection =
    selectedIcps && selectedIcps.length > 0
      ? `
    <IdealCustomerProfiles>
        ${selectedIcps
          .map((icp) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof icp === 'string') {
              // Backward compatibility: find ICP by ID
              const icpObj = icps.find((i) => i.id === icp);
              if (icpObj) {
                const icpData = icpObj.data && typeof icpObj.data === 'object' ? icpObj.data : {};
                return `<ICP name="${icpObj.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
              }
              return '';
            } else {
              // New format: use the ICP object directly
              const icpData = icp.data && typeof icp.data === 'object' ? icp.data : {};
              return `<ICP name="${icp.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n')}
    </IdealCustomerProfiles>`
      : '';

  return `
    <AUDIENCE_CONTEXT>
        ${personasSection}
        ${icpsSection}
    </AUDIENCE_CONTEXT>
  `;
}

/**
 * Generate research materials context
 */
function generateResearchMaterials(selectedResearch: any[] = [], researchItems: any[] = []): string {
  if (!selectedResearch || selectedResearch.length === 0) return '';

  const researchSection = selectedResearch
    .map((research) => {
      // Handle both object format (new) and ID format (backward compatibility)
      if (typeof research === 'string') {
        // Backward compatibility: find research by ID
        const researchObj = researchItems.find((r) => r.id === research);
        if (researchObj) {
          return `<ResearchItem title="${researchObj.title || researchObj.topic || 'Research Item'}">\n<Content>${researchObj.description || 'Research content'}</Content>\n</ResearchItem>`;
        }
        return '';
      } else {
        return `<ResearchItem title="${research.title || research.topic || 'Research Item'}">\n<Content>${research?.description  || 'Research content'}</Content>\n</ResearchItem>`;
      }
    })
    .filter(Boolean) // Remove empty strings
    .join('\n');

  return `
    <RESEARCH_MATERIALS>
        ${researchSection}
    </RESEARCH_MATERIALS>
  `;
}

/**
 * Generate product knowledge base context
 */
function generateProductKnowledgeBase(selectedDocuments: any[] = []): string {
  if (!selectedDocuments || selectedDocuments.length === 0) return '';

  const documentsSection = selectedDocuments
    .map((doc) => {
      return `<Document title="${doc.name || 'Document'}">\n<Content>${doc.content || 'Document content'}</Content>\n</Document>`;
    })
    .join('\n');

  return `
    <PRODUCT_KNOWLEDGE_BASE>
        ${documentsSection}
    </PRODUCT_KNOWLEDGE_BASE>
  `;
}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: StudioContentParams): Promise<{ config: any; messages: any }> {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });

  // Generate the context blocks for compilation
  const personasBlock = generateAudienceContext(params.selectedPersonas || [], params.personas || [], [], []);
  const icpsBlock = generateAudienceContext([], [], params.selectedIcps || [], params.icps || []);
  const researchBlock = generateResearchMaterials(params.selectedResearch || [], params.researchItems || []);
  const documentsBlock = generateProductKnowledgeBase(params.selectedDocuments || []);

  // Compile the prompt with the variables
  const compiled = prompt.compile({
    channel: params.selectedCompanyContent?.channel || 'Not specified',
    content_type: params.selectedCompanyContent?.content_type || 'Not specified',
    task_description: params.taskDescription,
    personas_block: personasBlock,
    icps_block: icpsBlock,
    research_block: researchBlock,
    documents_block: documentsBlock,
    seo_keywords: (params.seoKeywords || []).join(', '),
    trend_keywords: (params.trendKeywords || []).join(', '),
    brand_guidelines: JSON.stringify(params.companyBrand || {})
  });

  return {
    config: prompt.config,
    messages: compiled
  };
}

/**
 * Generates content with chunk-by-chunk database updates for real-time streaming
 */
async function generateWithChunking(params: StudioContentParams, config: any, messages: any): Promise<StudioContentResult> {
  let accumulatedContent = '';

  // Maintain stable block IDs during streaming so the client can apply minimal updates
  const makeParagraphBlock = (id: string, text: string) => ({
    id,
    type: 'paragraph',
    content: [
      {
        type: 'text',
        text,
        styles: {},
      },
    ],
  });

  let blocks: any[] = [];
  const contentId: string = params.selectedCompanyContent.id;

  try {
    // Initialize OpenAI client for streaming
    const client = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    });

    const langfuseClient = observeOpenAI(client);

    // Seed DB with a placeholder block to kick off streaming (stable id)
    const firstBlockId = `stream-${contentId}-1`;
    blocks = [makeParagraphBlock(firstBlockId, '')];
    await sql`
      UPDATE company_content
      SET content_editor_template = ${JSON.stringify(blocks)}, is_generating = true, updated_at = ${new Date()}
      WHERE id = ${contentId}
    `;

    // Create streaming request
    const stream = await langfuseClient.chat.completions.create({
      model: "google/gemini-2.5-pro-preview",
      temperature: config.temperature || 0.7,
      messages: [{ role: "user", content: "" }, ...messages],
      stream: true,
    });

    let blockCounter = 1;

    // Helper to append text to the last block, creating new blocks on newlines
    const appendText = async (text: string) => {
      const parts = text.split(/\n/);
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        // Append text to last block
        const last = blocks[blocks.length - 1];
        const lastTextNode = last.content?.find((n: any) => n.type === 'text');
        if (lastTextNode) {
          lastTextNode.text = (lastTextNode.text || '') + part;
        } else {
          last.content = [{ type: 'text', text: part, styles: {} }];
        }

        // If this wasn't the last segment, it means there was a newline → create a new paragraph block
        if (i !== parts.length - 1) {
          blockCounter += 1;
          const newId = `stream-${contentId}-${blockCounter}`;
          blocks.push(makeParagraphBlock(newId, ''));
        }
      }

      // Persist minimal changes (same ids) → clients can update last block only
      await sql`
        UPDATE company_content
        SET content_editor_template = ${JSON.stringify(blocks)}, is_generating = true, updated_at = ${new Date()}
        WHERE id = ${contentId}
      `;
    };

    // Process streaming response chunk by chunk
    for await (const chunk of stream) {
      const content = (chunk as any).choices?.[0]?.delta?.content || '';
      if (!content) continue;
      accumulatedContent += content;
      await appendText(content);
    }

    // Finalize: mark as not generating
    await sql`
      UPDATE company_content
      SET content_editor_template = ${JSON.stringify(blocks)}, is_generating = false, updated_at = ${new Date()}
      WHERE id = ${contentId}
    `;

    return {
      content: accumulatedContent,
      content_editor_template: blocks,
    };
  } catch (error) {
    // Mark as not generating on error
    try {
      await sql`
        UPDATE company_content
        SET is_generating = false, error_generating = true, updated_at = ${new Date()}
        WHERE id = ${contentId}
      `;
    } catch {}
    throw error;
  }
}

/**
 * Generates studio content using Langfuse prompt and LLM with chunk-by-chunk updates
 */
export async function generateStudioContent(params: StudioContentParams): Promise<StudioContentResult> {
  try {
    console.log('Generating studio content with params:', params);

    // Compile the prompt with parameters
    const { config, messages } = await compilePrompt(params);
    console.log('Compiled prompt:', messages);

    // Check if we should use streaming (chunk-by-chunk updates)
    const useStreaming = params.selectedCompanyContent?.id && params.enableStreaming !== false;

    if (useStreaming) {
      // Use streaming with chunk-by-chunk database updates
      return await generateWithChunking(params, config, messages);
    } else {
      // Fallback to original non-streaming approach
      const response = await callLLM(
        "", // Empty string for chat-based prompts
        config, // Use the config portion of the prompt
        "google/gemini-2.5-pro-preview",
        { parse: false },
        messages // Pass compiled messages as additionalMessages
      );

      console.log('LLM response:', response);

      // Convert text content to BlockNote blocks using official server utilities
      const contentBlocks = await convertTextToBlocks(response);

      // Return standardized response format
      return {
        content: response,
        content_editor_template: contentBlocks,
      };
    }

  } catch (error) {
    console.error('Error generating studio content:', error);
    throw new Error(
      `Failed to generate studio content: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
