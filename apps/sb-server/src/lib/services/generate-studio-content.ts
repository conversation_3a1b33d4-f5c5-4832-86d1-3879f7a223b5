import dotenv from 'dotenv';
import { Langfuse } from "langfuse";

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

export interface StudioContentParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: any[];
  personas: any[];
  selectedIcps: any[];
  icps: any[];
  selectedResearch: any[];
  researchItems: any[];
  selectedDocuments: any[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
  enableStreaming?: boolean;
}

export interface StudioContentResult {
  content: string;
  content_editor_template: any[];
  compiled_prompt?: {
    config: any;
    messages: any;
    model: string;
  };
}

// Server-side text to blocks conversion removed - content generation now happens on frontend using BlockNote AI extension

/**
 * Generate context blocks for audience information
 */
function generateAudienceContext(
  selectedPersonas: any[] = [],
  personas: any[] = [],
  selectedIcps: any[] = [],
  icps: any[] = []
): string {
  const personasSection =
    selectedPersonas && selectedPersonas.length > 0
      ? `
    <Personas>
        ${selectedPersonas
          .map((persona) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof persona === 'string') {
              // Backward compatibility: find persona by ID
              const personaObj = personas.find((p) => p.id === persona);
              if (personaObj) {
                const personaData = personaObj.data && typeof personaObj.data === 'object' ? personaObj.data : {};
                return `<Persona name="${personaObj.name}">\n<Description>${JSON.stringify((personaData as any).data) || 'Target persona'}</Description>\n</Persona>`;
              }
              return '';
            } else {
              // New format: use the persona object directly
              const personaData = persona.data && typeof persona.data === 'object' ? persona.data : {};
              return `<Persona name="${persona.name}">\n<Description>${JSON.stringify((personaData as any).data) || 'Target persona'}</Description>\n</Persona>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n')}
    </Personas>`
      : '';

  const icpsSection =
    selectedIcps && selectedIcps.length > 0
      ? `
    <IdealCustomerProfiles>
        ${selectedIcps
          .map((icp) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof icp === 'string') {
              // Backward compatibility: find ICP by ID
              const icpObj = icps.find((i) => i.id === icp);
              if (icpObj) {
                const icpData = icpObj.data && typeof icpObj.data === 'object' ? icpObj.data : {};
                return `<ICP name="${icpObj.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
              }
              return '';
            } else {
              // New format: use the ICP object directly
              const icpData = icp.data && typeof icp.data === 'object' ? icp.data : {};
              return `<ICP name="${icp.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n')}
    </IdealCustomerProfiles>`
      : '';

  return `
    <AUDIENCE_CONTEXT>
        ${personasSection}
        ${icpsSection}
    </AUDIENCE_CONTEXT>
  `;
}

/**
 * Generate research materials context
 */
function generateResearchMaterials(selectedResearch: any[] = [], researchItems: any[] = []): string {
  if (!selectedResearch || selectedResearch.length === 0) return '';

  const researchSection = selectedResearch
    .map((research) => {
      // Handle both object format (new) and ID format (backward compatibility)
      if (typeof research === 'string') {
        // Backward compatibility: find research by ID
        const researchObj = researchItems.find((r) => r.id === research);
        if (researchObj) {
          return `<ResearchItem title="${researchObj.title || researchObj.topic || 'Research Item'}">\n<Content>${researchObj.description || 'Research content'}</Content>\n</ResearchItem>`;
        }
        return '';
      } else {
        return `<ResearchItem title="${research.title || research.topic || 'Research Item'}">\n<Content>${research?.description  || 'Research content'}</Content>\n</ResearchItem>`;
      }
    })
    .filter(Boolean) // Remove empty strings
    .join('\n');

  return `
    <RESEARCH_MATERIALS>
        ${researchSection}
    </RESEARCH_MATERIALS>
  `;
}

/**
 * Generate product knowledge base context
 */
function generateProductKnowledgeBase(selectedDocuments: any[] = []): string {
  if (!selectedDocuments || selectedDocuments.length === 0) return '';

  const documentsSection = selectedDocuments
    .map((doc) => {
      return `<Document title="${doc.name || 'Document'}">\n<Content>${doc.content || 'Document content'}</Content>\n</Document>`;
    })
    .join('\n');

  return `
    <PRODUCT_KNOWLEDGE_BASE>
        ${documentsSection}
    </PRODUCT_KNOWLEDGE_BASE>
  `;
}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: StudioContentParams): Promise<{ config: any; messages: any }> {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });

  // Generate the context blocks for compilation
  const personasBlock = generateAudienceContext(params.selectedPersonas || [], params.personas || [], [], []);
  const icpsBlock = generateAudienceContext([], [], params.selectedIcps || [], params.icps || []);
  const researchBlock = generateResearchMaterials(params.selectedResearch || [], params.researchItems || []);
  const documentsBlock = generateProductKnowledgeBase(params.selectedDocuments || []);

  // Compile the prompt with the variables
  const compiled = prompt.compile({
    channel: params.selectedCompanyContent?.channel || 'Not specified',
    content_type: params.selectedCompanyContent?.content_type || 'Not specified',
    task_description: params.taskDescription,
    personas_block: personasBlock,
    icps_block: icpsBlock,
    research_block: researchBlock,
    documents_block: documentsBlock,
    seo_keywords: (params.seoKeywords || []).join(', '),
    trend_keywords: (params.trendKeywords || []).join(', '),
    brand_guidelines: JSON.stringify(params.companyBrand || {})
  });

  return {
    config: prompt.config,
    messages: compiled
  };
}

// Server-side streaming removed - content generation now happens on frontend using BlockNote AI extension

/**
 * Generates studio content prompt compilation for frontend LLM generation
 */
export async function generateStudioContent(params: StudioContentParams): Promise<StudioContentResult> {
  try {
    console.log('Compiling studio content prompt with params:', params);

    // Compile the prompt with parameters
    const { config, messages } = await compilePrompt(params);
    console.log('Compiled prompt:', messages);

    // Return compiled prompt for frontend LLM generation
    return {
      content: '', // Empty content since generation happens on frontend
      content_editor_template: [],
      compiled_prompt: {
        config,
        messages,
        model: "google/gemini-2.5-pro-preview"
      }
    };
  } catch (error) {
    console.error('Error compiling studio content prompt:', error);
    throw new Error(
      `Failed to compile studio content prompt: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
